import React, { createContext, useContext, useState, useEffect } from 'react';
import { getSetting } from '../constants/Storage';
import { ThemeMode } from '../constants/Theme';

interface SettingsContextType {
    currency: string;
    language: string;
    hideBudgetModule: boolean;
    hideMemberSection: boolean;
    hideExcludeFromBudget: boolean;
    hideShoppingPlatformSection: boolean;
    themeMode: ThemeMode;
    refreshSettings: () => void;
}

const SettingsContext = createContext<SettingsContextType>({
    currency: '¥',
    language: 'zh',
    hideBudgetModule: false,
    hideMemberSection: false,
    hideExcludeFromBudget: false,
    hideShoppingPlatformSection: false,
    themeMode: 'light',
    refreshSettings: () => { },
});

export const SettingsProvider = ({ children }: { children: React.ReactNode }) => {
    const [currency, setCurrency] = useState('¥');
    const [language, setLanguage] = useState('zh');
    const [hideBudgetModule, setHideBudgetModule] = useState(false);
    const [hideMemberSection, setHideMemberSection] = useState(false);
    const [hideExcludeFromBudget, setHideExcludeFromBudget] = useState(false);
    const [hideShoppingPlatformSection, setHideShoppingPlatformSection] = useState(false);
    const [themeMode, setThemeMode] = useState<ThemeMode>('light');
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    useEffect(() => {
        const loadSettings = async () => {
            const savedCurrency = await getSetting('currency');
            if (savedCurrency) {
                setCurrency(savedCurrency);
            }

            const savedLanguage = await getSetting('language');
            if (savedLanguage) {
                setLanguage(savedLanguage);
            }

            const savedHideBudgetModule = await getSetting('hideBudgetModule');
            if (savedHideBudgetModule) {
                setHideBudgetModule(savedHideBudgetModule === 'true');
            }

            const savedHideMemberSection = await getSetting('hideMemberSection');
            if (savedHideMemberSection) {
                setHideMemberSection(savedHideMemberSection === 'true');
            }

            const savedHideExcludeFromBudget = await getSetting('hideExcludeFromBudget');
            if (savedHideExcludeFromBudget) {
                setHideExcludeFromBudget(savedHideExcludeFromBudget === 'true');
            }

            const savedHideShoppingPlatformSection = await getSetting('hideShoppingPlatformSection');
            if (savedHideShoppingPlatformSection) {
                setHideShoppingPlatformSection(savedHideShoppingPlatformSection === 'true');
            }

            const savedThemeMode = await getSetting('themeMode');
            if (savedThemeMode && (savedThemeMode === 'light' || savedThemeMode === 'dark')) {
                setThemeMode(savedThemeMode as ThemeMode);
            }
        };

        loadSettings();
    }, [refreshTrigger]);

    const refreshSettings = () => {
        setRefreshTrigger(prev => prev + 1);
    };

    return (
        <SettingsContext.Provider value={{ currency, language, hideBudgetModule, hideMemberSection, hideExcludeFromBudget, hideShoppingPlatformSection, themeMode, refreshSettings }}>
            {children}
        </SettingsContext.Provider>
    );
};

export const useSettings = () => useContext(SettingsContext); 